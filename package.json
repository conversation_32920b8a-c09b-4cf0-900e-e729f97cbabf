{"name": "thinkline-cli", "version": "0.1.0", "description": "An agentic command line tool that allows developers to delegate coding tasks directly from their terminal", "main": "dist/index.js", "bin": {"thinkline": "dist/cli.js", "tl": "dist/cli.js"}, "scripts": {"build": "tsc", "dev": "tsx src/cli.ts", "start": "node dist/cli.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "prepare": "npm run build", "prepublishOnly": "npm run test && npm run build"}, "keywords": ["cli", "ai", "coding-assistant", "automation", "development-tools", "natural-language", "code-generation"], "author": "Your Name", "license": "MIT", "engines": {"node": ">=18.0.0"}, "dependencies": {"commander": "^11.1.0", "chalk": "^5.3.0", "ora": "^7.0.1", "inquirer": "^9.2.12", "fs-extra": "^11.1.1", "glob": "^10.3.10", "yaml": "^2.3.4", "dotenv": "^16.3.1", "axios": "^1.6.2", "openai": "^4.20.1"}, "devDependencies": {"@types/node": "^20.10.0", "@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.7", "@types/jest": "^29.5.8", "typescript": "^5.3.2", "tsx": "^4.6.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "prettier": "^3.1.0"}}